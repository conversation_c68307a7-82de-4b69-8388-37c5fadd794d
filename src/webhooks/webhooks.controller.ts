import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, Query, DefaultValuePipe, ParseIntPipe } from '@nestjs/common';
import { WebhooksService } from './webhooks.service';
import { CreateWebhookDto } from './dto/create-webhook.dto';
import { UpdateWebhookDto } from './dto/update-webhook.dto';
import { ApiSecurity } from '@nestjs/swagger';
import { ApiKeyAuthGuard } from 'src/auth/api-key.guard';
import { ActivateWebhookDto } from './dto/activate-webhook.dto';

@Controller('webhooks')
@UseGuards(ApiKeyAuthGuard)
export class WebhooksController {
  constructor(private readonly webhooksService: WebhooksService) {}

  @Post('create')
  @ApiSecurity('x-api-key')
  async create(@Body() createWebhookDto: CreateWebhookDto , @Request() req) {
    return this.webhooksService.createWebhook(createWebhookDto, req.user.id);
  }


 @Get('getAll')
  @ApiSecurity('x-api-key')
  async getAllWebhooks( @Request() req: any,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number
) {
    return this.webhooksService.getAllWebhooks(
      req.user.id,
      limit ?? 20,   // default if not provided
      offset ?? 0 
    );
  }


  @Get('get/:id')
  @ApiSecurity('x-api-key')
  async getWebhook(@Param('id') id: string) {
    return this.webhooksService.getWebhookById(id);
  }

  @Post('update/:id')
  @ApiSecurity('x-api-key')
  async updateWebhook(@Param('id') id: string, @Body() updateWebhookDto: UpdateWebhookDto) {
    return this.webhooksService.updateWebookById(id, updateWebhookDto);
  }

  @Post('delete/:id')
  @ApiSecurity('x-api-key')
  async deleteWebhook(@Param('id') id: string) {
    return this.webhooksService.deleteWebhookById(id);
  }

  @Post('pause/:id')
  @ApiSecurity('x-api-key')
  async pauseWebhook(@Param('id') id: string) {
    return this.webhooksService.pauseWebhook(id);
  }

  @Post('activete/:id')
  @ApiSecurity('x-api-key')
  async activateWebhook(@Param ('id') id: string , @Body() activateWebhookDto: ActivateWebhookDto) {
    return this.webhooksService.activateWebhook(id , activateWebhookDto.startFrom);
  }

  @Post('deleteAll')
  @ApiSecurity('x-api-key')
  async deleteAllUserWebhooks(@Request() req) {
    return this.webhooksService.deleteAllUserWebhooks(req.user.id);
  }

}
