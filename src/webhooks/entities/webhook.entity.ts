import { Column, CreateDate<PERSON>olumn, DeleteDateColumn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

enum status {
  ACTIVE = 'active',
  PAUSED = 'paused',
  TERMINATED = 'terminated',
  RESUMING = 'resuming',
}

enum addressType {
  WALLET = 'wallet',
  CONTRACT = 'contract',
}

@Entity('webhook')
export class Webhook {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'int' })
  user_id: number;

  @Column({ type: 'int' })
  network_id: number;

  @Column({ type: 'int', })
  monitored_addresses_id: number;

  @Column({ type: 'varchar'})
  name: string;

  @Column({ type: 'varchar'})
  webhook_url: string;

  @Column({ type: 'varchar'})
  security_tokens: string // for HMAC signatures;

  @Column({ type: 'int' , default: 0 })
  start_position: number; 
  
  @Column({ type: 'enum', enum: status })
  status: status;

  @Column({ type: 'varchar', nullable: true , default: null})
  tags: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @DeleteDateColumn({ type: 'timestamp', nullable: true  , default: null })
  deleted_at: Date ;
}
