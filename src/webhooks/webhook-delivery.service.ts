import { Injectable, Logger } from "@nestjs/common";
import { InjectEntityManager } from "@nestjs/typeorm";
import { MonitoredAddress } from "src/webhooks/entities/monitored-adddresses.entity";
import { EntityManager } from "typeorm";
import { Webhook } from "./entities/webhook.entity";
import { Network } from "src/networks/entities/network.entity";
import * as crypto from 'crypto';

interface TransactionData {
  hash: string;
  from: string;
  to: string;
  value: string;
  blockNumber: string;
  blockTimestamp: string;
  gasPrice?: string;
  transactionIndex?: string;
  nonce?: string;
}

interface WebhookPayload {
  webhook_id: string;
  network: string;
  transactions: TransactionData[];
  timestamp: string;
}

@Injectable()
export class WebhookDeliveryService {
  private readonly logger = new Logger(WebhookDeliveryService.name);

  constructor(
    @InjectEntityManager()
    private entityManager: EntityManager
  ) {}

  // generate signature 
  generateSignature(payload: string, secret: string): string {
    return crypto
      .createHmac('sha256', secret)
      .update(payload, 'utf8')
      .digest('hex');
  }


  //find webhooks that monitor specific addresses
  async findMatchingWebhooks(networkId: number, addresses: string[]): Promise<any[]> {
    if (!addresses || addresses.length === 0) {
      return [];
    }

    // Normalize for db can index ez 
    const normalizedAddresses = addresses.map(addr => addr.toLowerCase());

    const webhooks = await this.entityManager
      .createQueryBuilder(MonitoredAddress, 'ma')
      .innerJoin(Webhook, 'w', 'ma.webhook_id = w.id')
      .innerJoin(Network, 'n', 'w.network_id = n.id')
      .select([
        'DISTINCT w.id as webhook_id',
        'w.webhook_url as webhook_url', 
        'w.security_tokens as security_token',
        'w.user_id as user_id',
        'n.display_name as network_name'
      ])
      .where('ma.address IN (:...addresses)', { addresses: normalizedAddresses })
      .andWhere('ma.is_active = true')
      .andWhere('w.status = :status', { status: 'active' })
      .andWhere('w.network_id = :networkId', { networkId })
      .andWhere('w.deleted_at IS NULL')
      .getRawMany();

    this.logger.log(`Found ${webhooks.length} matching webhooks for addresses: ${normalizedAddresses.join(', ')}`);
    return webhooks;
  }

  // send HTTP request to webhook URL 
  async sendWebhookRequest(webhookUrl: string, payload: WebhookPayload, securityToken: string): Promise<boolean> {
    try {
      const payloadString = JSON.stringify(payload);
      const signature = this.generateSignature(payloadString, securityToken);

      const response = await fetch(webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Webhook-Signature': `sha256=${signature}`,
          'User-Agent': 'ChainPipe-Webhook/1.0'
        },
        body: payloadString
      });

      if (response.ok) {
        this.logger.log(`Successfully sent webhook to ${webhookUrl}`);
        return true;
      } else {
        this.logger.error(`Webhook failed: ${webhookUrl} - Status: ${response.status}`);
        return false;
      }
      
    } catch (error) {
      this.logger.error(`Webhook error: ${webhookUrl} - ${error.message}`);
      return false;
    }
  }

  // Main function to route transactions to matching webhooks
  async routeTransactionsToWebhooks(networkId: number, transactions: TransactionData[], networkName: string): Promise<void> {
    if (!transactions || transactions.length === 0) {
      this.logger.log('No transactions to route');
      return;
    }

    // Extract all unique addresses from transactions (from + to)
    const allAddresses = new Set<string>();
    transactions.forEach(tx => {
      if (tx.from) allAddresses.add(tx.from.toLowerCase());
      if (tx.to) allAddresses.add(tx.to.toLowerCase());
    });

    const uniqueAddresses = Array.from(allAddresses);
    this.logger.log(`Routing ${transactions.length} transactions involving ${uniqueAddresses.length} addresses`);

    // Find webhooks monitoring these addresses
    const matchingWebhooks = await this.findMatchingWebhooks(networkId, uniqueAddresses);

    if (matchingWebhooks.length === 0) {
      this.logger.log('No webhooks found for these addresses');
      return;
    }

    // Group transactions by webhook
    const webhookTransactions = new Map<string, TransactionData[]>();

    for (const webhook of matchingWebhooks) {
      const webhookId = webhook.webhook_id;

      // check what addresses got 
      const monitoredAddresses = await this.getWebhookAddresses(webhookId);
      
      // Filter transactions Match & Match this webhook's addresses
      const relevantTransactions = transactions.filter(tx => {
        const fromMatch = tx.from && monitoredAddresses.includes(tx.from.toLowerCase());
        const toMatch = tx.to && monitoredAddresses.includes(tx.to.toLowerCase());
        return fromMatch || toMatch;
      });

      if (relevantTransactions.length > 0) {
        webhookTransactions.set(webhookId, relevantTransactions);
      }
    }

    // Send to each webhook 
    const deliveryPromises = [];
    
    for (const [webhookId, txList] of webhookTransactions) {
      const webhook = matchingWebhooks.find(w => w.webhook_id === webhookId);
      
      const payload: WebhookPayload = {
        webhook_id: webhookId,
        network: networkName,
        transactions: txList,
        timestamp: new Date().toISOString()
      };

      const deliveryPromise = this.sendWebhookRequest(
        webhook.webhook_url,
        payload,
        webhook.security_token
      );

      deliveryPromises.push(deliveryPromise);
    }
 
    // delievery to all webhooks
    const results = await Promise.allSettled(deliveryPromises);
    const successful = results.filter(r => r.status === 'fulfilled' && r.value).length;
    const failed = results.length - successful;

    this.logger.log(`📊 Webhook delivery complete: ${successful} successful, ${failed} failed`);
  }

  // Get addresses by id 
  private async getWebhookAddresses(webhookId: string): Promise<string[]> {
    const addresses = await this.entityManager
      .createQueryBuilder(MonitoredAddress, 'ma')
      .select('ma.address')
      .where('ma.webhook_id = :webhookId AND ma.is_active = true', { webhookId })
      .getRawMany();

    return addresses.map(row => row.address.toLowerCase());
  }


  // create fake transactions for weburl testing  
  async testWebhookDelivery(networkId: number, networkName: string): Promise<void> {
    const testTransactions: TransactionData[] = [
      {
        hash: "0x123abc...",
        from: "0x31384E21D3df6F69DB15859DBE0e130ceab2398e",
        to: "0xEd7923a4F881F26C3d76ed17D28cc44dDa5Cd4C8",
        value: "0x58d15e17628000",
        blockNumber: "0x162428c",
        blockTimestamp: "0x68aae1af"
      },
      {
        hash: "0x456def...",
        from: "0xEd7923a4F881F26C3d76ed17D28cc44dDa5Cd4C8",
        to: "0x742d35cc6097db19b6c2e4a6d3f3b8b1a1d2d8b1",
        value: "0x29a2241af62c0000",
        blockNumber: "0x162428d",
        blockTimestamp: "0x68aae1b0"
      }
    ];

    this.logger.log('🧪 Testing webhook delivery...');
    await this.routeTransactionsToWebhooks(networkId, testTransactions, networkName);
  }







}

