import { Module } from '@nestjs/common';
import { WebhooksService } from './webhooks.service';
import { WebhooksController } from './webhooks.controller';
import { NetworksModule } from 'src/networks/networks.module';
import { ApiKeyModule } from 'src/api-key/api-key.module';
import { WebhookDeliveryService } from './webhook-delivery.service';

@Module({
  controllers: [WebhooksController],
  providers: [WebhooksService],
  imports: [NetworksModule, ApiKeyModule],
  exports: [WebhookDeliveryService]
})
export class WebhooksModule {}
