import { BadRequestException, Injectable } from '@nestjs/common';
import { CreateWebhookDto } from './dto/create-webhook.dto';
import { UpdateWebhookDto } from './dto/update-webhook.dto';
import { EntityManager } from 'typeorm';
import { InjectEntityManager } from '@nestjs/typeorm';
import * as crypto from 'crypto';
import { Webhook } from './entities/webhook.entity';
import { User } from 'src/users/entities/user.entity';
import { NetworksService } from 'src/networks/networks.service';
import { ApiKeyService } from 'src/api-key/api-key.service';
import { Network } from 'src/networks/entities/network.entity';
import {WebhookActiveStatus, WebhookStatus } from './webhooks.status.enum';
import { MonitoredAddress } from 'src/webhooks/entities/monitored-adddresses.entity';

@Injectable()
export class WebhooksService {
  private readonly MAX_ADDRESSES_PER_WEBHOOK = 1000;

  constructor( 
    @InjectEntityManager()
    private entityManager : EntityManager,
    private networksService: NetworksService,
    private apiKeyService: ApiKeyService,
  ){}

  // ==================== HELPER METHODS ====================

  async generateSecurityToken(): Promise<string> {
    const prefix = 'wsLAF_';
    const token = crypto.randomBytes(32).toString('base64');
    return `${prefix}${token}`;
  }

  async checkIfWebhookExists(id: string): Promise<boolean> {
    if (!id) {
      throw new BadRequestException('Invalid webhook ID');
    }
    
    const webhook = await this.entityManager
      .createQueryBuilder(Webhook, 'webhook')
      .where('webhook.id = :id AND webhook.deleted_at IS NULL', { id })
      .getOne();
    
    if (!webhook) {
      throw new BadRequestException(`Webhook with ID ${id} not found`);
    }
    return true;
  }

  async validateWebhookOwnership(user_id: number, webhook_name: string){
    const validateWebhookName = await this.entityManager
      .createQueryBuilder(Webhook, 'webhook')
      .where('webhook.user_id = :user_id AND webhook.name = :webhook_name', {
        user_id,
        webhook_name,
      })
      .getOne();

    if (validateWebhookName) {
      throw new BadRequestException('Webhook name already exists for this user');
    }
  }

  async validateAddressLimit(addresses: string[]): Promise<void> {
    if (addresses && addresses.length > this.MAX_ADDRESSES_PER_WEBHOOK) {
      throw new BadRequestException(
        `Address limit exceeded. Maximum ${this.MAX_ADDRESSES_PER_WEBHOOK} addresses allowed per webhook. ` +
        `You provided ${addresses.length} addresses. Please create a new webhook for additional addresses.`
      );
    }
  }

  // ==================== MONITORED ADDRESSES METHODS ====================

  // Save addresses to monitored_addresses table
  async saveMonitoredAddresses(webhookId: string, userId: number, addresses: string[]): Promise<void> {
    if (!addresses || addresses.length === 0) {
      return;
    }

    const monitoredAddresses = addresses.map(address => ({
      user_id: userId,
      webhook_id: webhookId,
      address: address.toLowerCase(), // normalize them so db indexing doesnt 💥 up 
      is_active: true
    }));

    await this.entityManager
      .createQueryBuilder()
      .insert()
      .into(MonitoredAddress)
      .values(monitoredAddresses)
      .execute();
  }

  // Get all monitored addresses 
  async getMonitoredAddresses(webhookId: string): Promise<string[]> {
    console.log("getMonitoredAddresses webhookId" , webhookId)
    const addresses = await this.entityManager
      .createQueryBuilder(MonitoredAddress, 'ma')
      .where('ma.webhook_id = :webhookId AND ma.is_active = true', { webhookId })
      .select('ma.address', 'address')
      .getRawMany();

    return addresses.map(row => row.address);
  }

  // Update monitored addresses  
  async updateMonitoredAddresses(webhookId: string, userId: number, newAddresses: string[]): Promise<void> {
    // blow it up like hiroshima and colonize like christopher columbus 
    await this.entityManager
      .createQueryBuilder()
      .update(MonitoredAddress)
      .set({ is_active: false })
      .where('webhook_id = :webhookId', { webhookId })
      .execute();

    // Add new addresses
    await this.saveMonitoredAddresses(webhookId, userId, newAddresses);
  }

  // ==================== WEBHOOK METHODS ====================

  async createWebhook(createWebhookDto: CreateWebhookDto , user_id: number): Promise<any> {
    console.log("createWebhookDto", createWebhookDto)
    // use auth to check for user 
    try {
      // check if network is valid
      await this.networksService.validateNetwork(createWebhookDto.network);

      // check if user has a webhook with this name
      await this.validateWebhookOwnership(user_id, createWebhookDto.name);

      // check if address limit is exceeded
      await this.validateAddressLimit(createWebhookDto.wallet_addresses);

      // might wanna delete this ( redundant )
      const network =await this.networksService.findOneWithName(createWebhookDto.network);
      const currentBlockNumber = await this.networksService.getLatestSequence(createWebhookDto.network);
      const securityToken = await this.generateSecurityToken();

      const user = await this.entityManager
        .createQueryBuilder(User, 'user')
        .where('user.id = :user_id', { user_id: user_id })
        .getOne();

      if (!user) {
        throw new BadRequestException('User not found');
      }

      const insertResult = await this.entityManager
        .createQueryBuilder()
        .insert()
        .into(Webhook)
        .values({
          user_id: user_id,
          network_id: network.id,
          name: createWebhookDto.name,
          webhook_url: createWebhookDto.destination_attributes.url,
          security_tokens: securityToken,
          start_position: currentBlockNumber,
          status: createWebhookDto.status as any, // Cast to enum type
          wallet_addresses: createWebhookDto.wallet_addresses || [], 
        })
        .returning('*')
        .execute();

        const savedWebhook = insertResult.generatedMaps[0] as Webhook;

        await this.saveMonitoredAddresses(
          savedWebhook.id, 
          user_id, 
          createWebhookDto.wallet_addresses || []
        );

        const destinationAttributes = {
          url: createWebhookDto.destination_attributes.url,
          security_token: securityToken,
        };

        const monitorAddresses = {
          wallets: createWebhookDto.wallet_addresses || [],
        };

        return {
        id: savedWebhook.id,
        name: savedWebhook.name,
        status: savedWebhook.status,
        created_at: savedWebhook.created_at,
        destination_attributes: destinationAttributes,
        network: createWebhookDto.network,
        notification_email: user.email,
        sequence: currentBlockNumber,
        updated_at: savedWebhook.updated_at,
        monitorAddresses: monitorAddresses
      };
    } catch (error) {
      throw new BadRequestException("Failed to create webhook", error.message);
    }
  }

  async getAllWebhooks(user_id: number , limit: number, offset: number): Promise<{ data: any[]; pageInfo: any }> {
    const totalCount = await this.entityManager
      .createQueryBuilder(Webhook, 'webhook')
      .where('webhook.user_id = :user_id AND webhook.deleted_at IS NULL', { user_id })
      .getCount();

    const webhooks = await this.entityManager
      .createQueryBuilder(Webhook, 'webhook')
      .leftJoin(Network, 'network', 'network.id = webhook.network_id')
      .where('webhook.user_id = :user_id AND webhook.deleted_at IS NULL', { user_id })
      .orderBy('webhook.created_at', 'DESC')
      .select([
        'webhook.id AS webhook_id',
        'webhook.name AS name',
        'webhook.status AS status',
        'webhook.created_at AS created_at',
        'webhook.updated_at AS updated_at',
        'webhook.webhook_url AS webhook_url',
        'webhook.security_tokens AS security_tokens',
        'webhook.wallet_addresses AS wallet_addresses',
        'webhook.start_position AS start_position',
        'network.display_name AS network_display_name',
      ])
      .limit(limit)
      .offset(offset)
      .getRawMany();


      //await cannot work in map func that why need async func
      const data = await Promise.all(webhooks.map(async (webhook) => {
        let sequence: number;

        
      if (webhook.status === WebhookStatus.ACTIVE) {
        sequence =  await this.networksService.getLatestSequence(webhook.network_display_name) || 0;
      } else if (webhook.status === WebhookStatus.PAUSED) {
        sequence = webhook.start_position ? webhook.start_position : 0;
      } else { // any other state
        sequence = webhook.start_position ? webhook.start_position : 0;
      }

      const addresses = await this.getMonitoredAddresses(webhook.webhook_id);
      const monitorAddresses = {
        wallets: addresses,
      };

      return {
        id: webhook.webhook_id, 
        name: webhook.name,
        status: webhook.status,
        created_at: webhook.created_at,
        destination_attributes: {
          url: webhook.webhook_url,
          security_token: webhook.security_tokens,
        },
        network: webhook.network_display_name,
        sequence: sequence,
        monitorAddresses: monitorAddresses,
        updated_at: webhook.updated_at,
      };
    }));

    return {
      data,
      pageInfo: {
        limit,
        offset,
        total: totalCount, 
      },
    }
  }

  async getWebhookById(id: string): Promise<any> {
    const webhook = await this.entityManager
      .createQueryBuilder(Webhook, 'webhook')
      .leftJoin(Network, 'network', 'network.id = webhook.network_id')
      .where('webhook.id = :id AND webhook.deleted_at IS NULL', { id })
      .select([
        'webhook.id AS webhook_id',
        'webhook.name AS name',
        'webhook.status AS status',
        'webhook.created_at AS created_at',
        'webhook.updated_at AS updated_at',
        'webhook.webhook_url AS webhook_url',
        'webhook.security_tokens AS security_tokens',
        'webhook.wallet_addresses AS wallet_addresses',
        'network.display_name AS network_display_name',
      ])
      .getRawOne();

    const currentBlockNumber = await this.networksService.getLatestSequence(webhook.network_display_name);
    const addresses = await this.getMonitoredAddresses(webhook.webhook_id);
    const monitorAddresses = {
      wallets: addresses,
    };

    return {
      id: webhook.webhook_id, 
      name: webhook.name,
      status: webhook.status,
      created_at: webhook.created_at,
      destination_attributes: {
        url: webhook.webhook_url,
        security_token: webhook.security_tokens,
      },
      network: webhook.network_display_name,
      sequence: currentBlockNumber,
      monitorAddresses: monitorAddresses,
      updated_at: webhook.updated_at,
    };
  }

  async updateWebookById(id: string, updateWebhookDto: UpdateWebhookDto): Promise<any> {
    try {
      await this.checkIfWebhookExists(id);
      // check if paused 

      const updateData: any = {};
      if (updateWebhookDto.name) {
        updateData.name = updateWebhookDto.name;
      }
      
      if (updateWebhookDto.destination_attributes?.url) {
        updateData.webhook_url = updateWebhookDto.destination_attributes.url;
      }
      
      if (updateWebhookDto.status) {
        updateData.status = updateWebhookDto.status;
      }

      if (updateWebhookDto.monitorAddresses?.wallets) {
        updateData.wallet_addresses = updateWebhookDto.monitorAddresses.wallets;
      }

      const updateResult = await this.entityManager
        .createQueryBuilder()
        .update(Webhook)
        .set(updateData)
        .where('id = :id', { id })
        .returning('*')
        .execute();

      const updatedWebhook = updateResult.raw[0] as Webhook;

      // Update monitored addresses if provided
      if (updateWebhookDto.monitorAddresses?.wallets) {
        await this.updateMonitoredAddresses(
          id, 
          updatedWebhook.user_id, 
          updateWebhookDto.monitorAddresses.wallets
        );
      }

      // Get addresses 
      const addresses = await this.getMonitoredAddresses(id);
      const monitorAddresses = {
        wallets: addresses,
      };


      return {
        id: updatedWebhook.id, 
        name: updatedWebhook.name,
        status: updatedWebhook.status,
        created_at: updatedWebhook.created_at,
        destination_attributes: {
          url: updatedWebhook.webhook_url,
          security_token: updatedWebhook.security_tokens,
        },
        // dont allow update network
        sequence: updatedWebhook.start_position,  // on pause save current block
        monitorAddresses: monitorAddresses,
        updated_at: updatedWebhook.updated_at,
      };



    }catch (error) {
      throw new BadRequestException("Failed to update webhook", error.message);
    }
  }

  async deleteWebhookById(id: string): Promise<any> {
    try {
      await this.checkIfWebhookExists(id);

      await this.entityManager // soft delete ???????
        .createQueryBuilder()
        .update(MonitoredAddress)
        .set({ is_active: false })
        .where('webhook_id = :id', { id })
        .execute();

      await this.entityManager
        .createQueryBuilder()
        .update(Webhook)
        .set({ status: WebhookStatus.TERMINATED })
        .where('id = :id', { id })
        .execute();

      await this.entityManager
        .createQueryBuilder(Webhook, 'webhook')
        .where('id = :id', { id })
        .softDelete()
        .execute();

      return { message: 'Webhook deleted successfully' };
    }catch(error){
      throw new BadRequestException("Failed to delete webhook", error.message);
    }
  }

  async pauseWebhook(id: string): Promise<any> {
    try {
      await this.checkIfWebhookExists(id);
      // saving current blocknumber in start_position
      const webhook = await this.entityManager
        .createQueryBuilder(Webhook, 'webhook')
        .leftJoin(Network, 'network', 'network.id = webhook.network_id')
        .where('webhook.id = :id AND webhook.deleted_at IS NULL', { id })
        .select(['network.display_name AS network_display_name'])
        .getRawOne();

      const currentBlockNumber = await this.networksService.getLatestSequence(webhook.network_display_name);

      // chnaging satus to paused
      await this.entityManager
        .createQueryBuilder()
        .update(Webhook)
        .set({ start_position: currentBlockNumber , status: WebhookStatus.PAUSED })
        .where('id = :id', { id })
        .returning('*')
        .execute();

      return { message: 'Webhook paused successfully' };
    }catch(error){
      throw new BadRequestException("Failed to pause webhook", error.message);
    }
  }

  async activateWebhook(id: string , startFrom: string): Promise<any> {
    try {
      await this.checkIfWebhookExists(id);
      // get current block number and start position
      const webhook = await this.entityManager
        .createQueryBuilder(Webhook, 'webhook')
        .leftJoin(Network, 'network', 'network.id = webhook.network_id')
        .where('webhook.id = :id', { id })
        .select(['webhook.start_position', 'network.display_name AS network_display_name'])
        .getRawOne();

      const currentBlockNumber = await this.networksService.getLatestSequence(webhook.network_display_name);

      let startPosition = 0;

      // if last / latest
      if (startFrom === WebhookActiveStatus.LAST) {
        startPosition = webhook.start_position;
      }
      else if (startFrom === WebhookActiveStatus.LATEST) {
        startPosition = currentBlockNumber;
      }
      else {
        throw new BadRequestException("Invalid startFrom value");
      }

      // save as active and save new start position
      await this.entityManager
        .createQueryBuilder()
        .update(Webhook)
        .set({ start_position: startPosition , status: WebhookStatus.ACTIVE })
        .where('id = :id', { id })
        .returning('*')
        .execute();

      return { message: 'Webhook activated successfully' };

    }catch(error){
      throw new BadRequestException("Failed to activate webhook", error.message);
    }
  }

  async deleteAllUserWebhooks(user_id: number): Promise<any> {
    try {
      // deactivate all addresses 4 dis webhook
      await this.entityManager
        .createQueryBuilder()
        .update(MonitoredAddress)
        .set({ is_active: false })
        .where('user_id = :user_id', { user_id })
        .execute();

      await this.entityManager
        .createQueryBuilder()
        .update(Webhook)
        .set({ status: WebhookStatus.TERMINATED })
        .where('user_id = :user_id', { user_id })
        .execute();
      
        await this.entityManager
        .createQueryBuilder(Webhook, 'webhook')
        .where('user_id = :user_id', { user_id })
        .softDelete()
        .execute();

      return { message: 'All user webhooks deleted successfully' };
    }catch(error){
      throw new BadRequestException("Failed to delete all user webhooks", error.message);
    }
  }

}
