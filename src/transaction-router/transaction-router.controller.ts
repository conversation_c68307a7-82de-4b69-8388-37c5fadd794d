import { <PERSON>, Get, Post, Body, Patch, Param, Delete, Logger } from '@nestjs/common';
import { TransactionRouterService } from './transaction-router.service';

@Controller('transaction-router')
export class TransactionRouterController {
  private readonly logger = new Logger(TransactionRouterController.name);

  constructor(
    private readonly transactionRouterService: TransactionRouterService
  ) {}

  // Quicknode sends transaction data to this endpoint
  @Post('receive/:network_id')
  async receiveTransactionData(
    @Param('network_id') networkId: string,
    @Body() payload: any
  ) {
    const startTime = Date.now();

    try {
      this.logger.log(`Received data for network ${networkId} 😗..........`);
      this.logger.log(`Received ${payload.data.length} blocks`);

      const result = await this.transactionRouterService.saveTransaction(
        parseInt(networkId), 
        payload  // Send the entire payload
      );

      const processingTime = Date.now() - startTime;

      if (result.success) {
        this.logger.log(`🤑 Successfully saved ${result.saved} transactions in ${processingTime}ms`);
        return {
          status: 'success',
          message: 'Transactions processed',
          transactionsSaved: result.saved,
          processingTime
        };
      } else {
        this.logger.error(`Service failed: ${result.error}`);
        return {
          status: 'error',
          message: result.error,
          processingTime
        };
      }
    }catch (error) {
      const processingTime = Date.now() - startTime;
      
      this.logger.error(
        `💥 BOOOMMMM!!! Controller error for network ${networkId}: ${error.message}`,
        error.stack
      );

      // return error for quicknode to retry
      return {
        status: 'error',
        message: 'Internal processing error',
        processingTime
      };
    }
  }

  // Test webhook delivery system
  @Post('test-webhooks/:network_id')
  async testWebhookDelivery(@Param('network_id') networkId: string) {
    try {
      this.logger.log(`Testing webhook delivery for network ${networkId}`);
      
      const result = await this.transactionRouterService.testWebhookDelivery(parseInt(networkId));
      
      return {
        status: 'success',
        message: 'Webhook delivery test initiated',
        network: result.network
      };
    } catch (error) {
      this.logger.error(`Test webhook delivery failed: ${error.message}`);
      return {
        status: 'error',
        message: error.message
      };
    }
  }

  // Fake quicknode data for testing - now with multiple transactions
  @Post('test/:network_id')
  async testWebhook(@Param('network_id') networkId: string) {
    const realWebhookPayload = {
      "data": [
        {
          "block": {
            "number": "0x162428c",
            "timestamp": "0x68aae1af",
            "transactions": [
              {
                "blockHash": "0x3c6348c3da63225904594b33c593f6d7a6f0d5b320be0f4e1289381asdasf0954",
                "blockNumber": "0x162428c",
                "from": "0x31384E21D3df6F69DB15859DBE0e130ceab2398e", // Address 1
                "gas": "0x5b2e",
                "gasPrice": "0x19da251a",
                "maxFeePerGas": "0x2d4cae00",
                "maxPriorityFeePerGas": "0x4b571c0",
                "hash": "0x123abc456def789",
                "input": "0x",
                "nonce": "0xb4",
                "to": "0xEd7923a4F881F26C3d76ed17D28cc44dDa5Cd4C8", // Address 2
                "transactionIndex": "0x30",
                "value": "0x58d15e17628000",
                "type": "0x2",
                "accessList": [],
                "chainId": "0x1"
              },
              {
                "blockHash": "0x3c6348c3da63225904594b33c593f6d7a6f0d5b320be0f4e1289381asdasf0955",
                "blockNumber": "0x162428c",
                "from": "0xEd7923a4F881F26C3d76ed17D28cc44dDa5Cd4C8", // Address 2
                "gas": "0x5b2e",
                "gasPrice": "0x19da251a",
                "hash": "0x456def789abc123",
                "input": "0x",
                "nonce": "0xb5",
                "to": "0x742d35cc6097db19b6c2e4a6d3f3b8b1a1d2d8b1", // Address 3
                "transactionIndex": "0x31",
                "value": "0x29a2241af62c0000",
                "type": "0x2",
                "chainId": "0x1"
              },
              {
                "blockHash": "0x3c6348c3da63225904594b33c593f6d7a6f0d5b320be0f4e1289381asdasf0956",
                "blockNumber": "0x162428c",
                "from": "0x31384E21D3df6F69DB15859DBE0e130ceab2398e", // Address 1 again
                "gas": "0x5b2e",
                "gasPrice": "0x19da251a",
                "hash": "0x789abc123def456",
                "input": "0x",
                "nonce": "0xb6",
                "to": "0x999888777666555444333222111000aaabbbcccd", // Unmonitored address
                "transactionIndex": "0x32",
                "value": "0x16345785d8a0000",
                "type": "0x2",
                "chainId": "0x1"
              },
              {
                "blockHash": "0x3c6348c3da63225904594b33c593f6d7a6f0d5b320be0f4e1289381asdasf0957",
                "blockNumber": "0x162428c",
                "from": "0x111222333444555666777888999aaabbbcccddde", // Unmonitored address
                "gas": "0x5b2e",
                "gasPrice": "0x19da251a",
                "hash": "0xabc123def456789",
                "input": "0x",
                "nonce": "0xb7",
                "to": "0x555666777888999aaabbbcccdddeeefffaabbcc", // Unmonitored address
                "transactionIndex": "0x33",
                "value": "0x6f05b59d3b20000",
                "type": "0x2",
                "chainId": "0x1"
              },
              {
                "blockHash": "0x3c6348c3da63225904594b33c593f6d7a6f0d5b320be0f4e1289381asdasf0958",
                "blockNumber": "0x162428c",
                "from": "0x742d35cc6097db19b6c2e4a6d3f3b8b1a1d2d8b1", // Address 3
                "gas": "0x5b2e",
                "gasPrice": "0x19da251a",
                "hash": "0xdef456789abc123",
                "input": "0x",
                "nonce": "0xb8",
                "to": "0x31384E21D3df6F69DB15859DBE0e130ceab2398e", // Address 1
                "transactionIndex": "0x34",
                "value": "0xde0b6b3a7640000",
                "type": "0x2",
                "chainId": "0x1"
              }
            ]
          }
        }
      ]
    };

    this.logger.log(`Testing with ${realWebhookPayload.data[0].block.transactions.length} transactions`);
    return await this.receiveTransactionData(networkId, realWebhookPayload);
  }
}
