import { <PERSON>, Get, Post, Body, Patch, Param, Delete, Logger } from '@nestjs/common';
import { TransactionRouterService } from './transaction-router.service';

@Controller('transaction-router')
export class TransactionRouterController {
  private readonly logger = new Logger(TransactionRouterController.name);

  constructor(private readonly transactionRouterService: TransactionRouterService) {}

  // Quicknode sends transaction data to this endpoint
  @Post('receive/:network_id')
  async receiveTransactionData(
    @Param('network_id') networkId: string,
    @Body() payload: any
  ) {
    const startTime = Date.now();

    try {
      this.logger.log(`Received data for network ${networkId} 😗..........`);
      this.logger.log(`Received ${payload.data.length} blocks`);

      const result = await this.transactionRouterService.saveTransaction(
        parseInt(networkId), 
        payload  // Send the entire payload
      );

      const processingTime = Date.now() - startTime;

      if (result.success) {
        this.logger.log(`🤑 Successfully saved ${result.saved} transactions in ${processingTime}ms`);
        return {
          status: 'success',
          message: 'Transactions processed',
          transactionsSaved: result.saved,
          processingTime
        };
      } else {
        this.logger.error(`❌ Service failed: ${result.error}`);
        return {
          status: 'error',
          message: result.error,
          processingTime
        };
      }
    }catch (error) {
      const processingTime = Date.now() - startTime;
      
      this.logger.error(
        `💥 BOOOMMMM!!! Controller error for network ${networkId}: ${error.message}`,
        error.stack
      );

      // return error for quicknode to retry
      return {
        status: 'error',
        message: 'Internal processing error',
        processingTime
      };
    }
  }

  // fake quicknode data delete this later 
  @Post('test/:network_id')
  async testWebhook(@Param('network_id') networkId: string) {
    const realWebhookPayload = {
      "data": [
        {
          "block": {
            "number": "0x162428c",
            "timestamp": "0x68aae1af",
            "transactions": [
              {
                "blockHash": "0x3c6348c3da63225904594b33c593f6d7a6f0d5b320be0f4e1289381asdasf0954",
                "blockNumber": "0x162428c",
                "from": "0xf153071970b4349f2ae33e5d1d42a7859d6f04a7",
                "gas": "0x5b2e",
                "gasPrice": "0x19da251a",
                "maxFeePerGas": "0x2d4cae00",
                "maxPriorityFeePerGas": "0x4b571c0",
                "hash": "sdaddassdsdasdasd",
                "input": "0x",
                "nonce": "0xb4",
                "to": "0x2e299baec2f480bd2d35435d9c28e1892b814327",
                "transactionIndex": "0x30",
                "value": "0x58d15e17628000",
                "type": "0x2",
                "accessList": [],
                "chainId": "0x1",
                "v": "0x1",
                "r": "0x411fd29df03adf531d8475b66a8cf9b1f9440426ba7eeaa40035d3f621c2361b",
                "s": "0x410606d5e122a070f7cabd91d018b92f77d8d3d9ad80335e3f413d176a769ed5",
                "yParity": "0x1"
              }
            ]
          }
        }
      ]
    };

    return await this.receiveTransactionData(networkId, realWebhookPayload);
  }

}
