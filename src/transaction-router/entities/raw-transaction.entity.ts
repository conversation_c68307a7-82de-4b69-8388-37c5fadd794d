import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity('raw_transactions')
export class RawTransactions {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'transaction_id', nullable: true })
  transactionId: number;

  @Column({ name: 'block_number', type: 'varchar', length: 20 })
  blockNumber: string;

  @Column({ name: 'raw_json', type: 'jsonb' })
  rawJson: any;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

}