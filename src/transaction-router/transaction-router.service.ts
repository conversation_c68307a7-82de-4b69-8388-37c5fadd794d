import { Injectable, Logger } from '@nestjs/common';
import { CreateTransactionRouterDto } from './dto/create-transaction-router.dto';
import { UpdateTransactionRouterDto } from './dto/update-transaction-router.dto';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { Transaction } from './entities/transactions.entity';
import { WebhookDeliveryService } from 'src/webhooks/webhook-delivery.service';
import { Network } from 'src/networks/entities/network.entity';

@Injectable()
export class TransactionRouterService {
  private readonly logger = new Logger(TransactionRouterService.name);

  constructor(
    @InjectEntityManager()
    private entityManager: EntityManager,
    private webhookDeliveryService: WebhookDeliveryService
  ) {}

  // ==================== TRANSACTION METHODS ====================
  async saveTransactions(networkId: number, rawData: any) {
    let totalTransactions = 0;
    let savedTransactions = 0;
    const allTransactions = [];

    // Debug: Log the structure of incoming data
    this.logger.debug('Raw data structure:', JSON.stringify(rawData, null, 2));

    try{
      for ( const blockData of rawData.data) { // loop by blocks
        // Validate block data structure
        if (!blockData || !blockData.block) {
          this.logger.warn('Invalid block data structure, skipping block');
          continue;
        }

        const result = await this.processBlock(networkId, blockData.block);

        // Safely access transactions length
        const transactionCount = blockData.block.transactions ? blockData.block.transactions.length : 0;
        totalTransactions += transactionCount;
        savedTransactions += result.saved;
        allTransactions.push(...result.transactions);
      }
      this.logger.log(`Total processed ${totalTransactions} transactions, saved ${savedTransactions} new ones`);

      return {
        success: true,
        saved: savedTransactions,
        total: totalTransactions,
        transactions: allTransactions
      };

    }catch(error){
      this.logger.error(`Transaction saving failed: ${error.message}`);
      return {
        success: false,
        error: error.message,
        saved: savedTransactions,
        total: totalTransactions,
        transactions: allTransactions
      };
    }
  }

  async processBlock(networkId :number, blockData: any){
    // uk i gotta validate 
    if (!blockData.transactions || blockData.transactions.length === 0) {
      return { saved: 0, transactions: [] };
    }

    // transactions to be saved 
    const dbTransactions = blockData.transactions.map((tx: any) => ({
      hash: tx.hash,
      networkId: networkId,
      from: tx.from ? tx.from.toLowerCase() : null,
      to: tx.to ? tx.to.toLowerCase() : null,
      value: tx.value,
      blockNumber: tx.blockNumber || blockData.number,
      blockTimestamp: blockData.timestamp,
      transactionIndex: tx.transactionIndex,
      nonce: tx.nonce,
      gasPrice: tx.gasPrice || tx.maxFeePerGas,
      gasUsed: tx.gas || null,
      chainId: tx.chainId || `0x${networkId}`,
      transactionType: tx.type,
      status: null
    }));

    // transactions struct to be sent to client webhooks
    const webhookTransactions = blockData.transactions.map((tx: any) => ({
      hash: tx.hash,
      from: tx.from?.toLowerCase() || null,
      to: tx.to?.toLowerCase() || null,
      value: tx.value,
      blockNumber: tx.blockNumber || blockData.number,
      blockTimestamp: blockData.timestamp,
      gasPrice: tx.gasPrice || tx.maxFeePerGas,
      transactionIndex: tx.transactionIndex,
      nonce: tx.nonce
    }));

    try {
      await this.entityManager
          .createQueryBuilder()
          .insert()
          .into(Transaction)
          .values(dbTransactions)
          .orIgnore()
          .execute();
      return {
        saved: dbTransactions.length,
        transactions: webhookTransactions
      };
    }catch(error){
      if (error.message.includes('duplicate key')) {
          this.logger.debug(`Skipped ${dbTransactions.length} duplicate transactions`);
          return {
            saved: 0,
            transactions: webhookTransactions
          };
        }
      throw error;
    }
  }

  async getNetwork(networkId: number): Promise<Network> {
    try {
      return await this.entityManager
        .createQueryBuilder(Network, 'network')
        .where('network.id = :networkId', { networkId })
        .getOne();
    } catch (error) {
      this.logger.error(`Failed to get network ${networkId}: ${error.message}`);
      return null;
    }
  }

  // connected to the webhookDeliveryService to deliver the webhooks 
  private async deliverWebhooks(networkId: number, transactions: any[], networkName: string) {
    try {
      this.logger.log(`WE deliverin ${transactions.length} transactions on ${networkName}`);
      
      const result = await this.webhookDeliveryService.routeTransactionsToWebhooks(
        networkId,
        transactions,
        networkName
      );

      return {
        found: result.webhooksFound,
        delivered: result.webhooksDelivered,
        failed: result.webhooksFailed,
        message: result.message
      };

    } catch (error) {
      this.logger.error(`Webhook delivery error: ${error.message}`);
      return {
        found: 0,
        delivered: 0,
        failed: 0,
        message: `Webhook delivery failed: ${error.message}`
      };
    }
  }

  // ==================== MAIN PROCESS ====================

  async processIncomingData(networkId: number, rawData: any) {
    // Step 1: Validate input ( always gotta do it ) 
     if (!rawData?.data || !Array.isArray(rawData.data)) {
      return {
        success: false,
        error: 'Invalid payload format - missing data array'
      };
    }

    // Step 2: Extract and save transactions
    const transactionResult = await this.saveTransactions(networkId, rawData);
    if(!transactionResult.success){
      return {
        success: false,
        error: `Transaction saving failed: ${transactionResult.error}`,
        transactions: { saved: 0, total: 0 }
      };
    }

    // Step 3: Get network info for webhook delievery 
    const network = await this.getNetwork(networkId);
    if (!network) {
      return {
        success: false,
        error: `Network ${networkId} not found`,
        transactions: transactionResult
      };
    }

    // Step 4: Deliver webhooks (if we have transactions)
    let webhookResult = { delivered: 0, failed: 0, found: 0, message: 'There are no ships in the sea Today' };

    if (transactionResult.transactions.length > 0) {
      webhookResult = await this.deliverWebhooks(networkId, transactionResult.transactions, network.display_name);
    }


    // Step 5: Return the pristinely formatted transactions 
    return {
      success: true,
      transactions: {
        saved: transactionResult.saved,
        total: transactionResult.total
      },
      webhooks: {
        found: webhookResult.found,
        delivered: webhookResult.delivered,
        failed: webhookResult.failed,
        message: webhookResult.message
      }
    };
  }

  
}
