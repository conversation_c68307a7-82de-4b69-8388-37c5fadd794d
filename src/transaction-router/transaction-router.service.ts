import { Injectable, Logger } from '@nestjs/common';
import { CreateTransactionRouterDto } from './dto/create-transaction-router.dto';
import { UpdateTransactionRouterDto } from './dto/update-transaction-router.dto';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { Transaction } from './entities/transactions.entity';
import { WebhookDeliveryService } from 'src/webhooks/webhook-delivery.service';
import { Network } from 'src/networks/entities/network.entity';

@Injectable()
export class TransactionRouterService {
  private readonly logger = new Logger(TransactionRouterService.name);

  constructor(
    @InjectEntityManager()
    private entityManager: EntityManager,
    private webhookDeliveryService: WebhookDeliveryService
  ) {}

  async saveTransaction(networkId: number, rawTxData: any){
    const startTime = Date.now();
    try {
      this.logger.log(`Processing webhook for network ${networkId}`);
      if (!rawTxData.data || !Array.isArray(rawTxData.data)) {
        throw new Error('Invalid webhook format');
      }

      let totalSaved = 0;
      let allTransactions = [];

      for (const blockData of rawTxData.data) {
        if (blockData.block && blockData.block.transactions) {
          const { savedCount, transactions } = await this.processBlockTransactions(networkId, blockData.block);
          totalSaved += savedCount;
          allTransactions.push(...transactions);
        }
      }

      const network = await this.entityManager
        .createQueryBuilder(Network, 'network')
        .where('network.id = :networkId', { networkId })
        .getOne();

      if (!network) {
        throw new Error(`Network with ID ${networkId} not found`);
      }

      if (allTransactions.length > 0) {
        this.logger.log(`VROOMMMM ( imagine car sounds ) Starting webhook delivery for ${allTransactions.length} transactions`);
        
        // Run webhook delivery in background (don't await to avoid blocking QuickNode responses) high level tactics 
        this.webhookDeliveryService.routeTransactionsToWebhooks(
          networkId, 
          allTransactions, 
          network.display_name
        ).catch(error => {
          this.logger.error(`Webhook delivery failed: ${error.message}`);
        });
      }

      const processingTime = Date.now() - startTime;
      this.logger.log(`Saved ${totalSaved} transactions in ${processingTime}ms`);

      return { 
        success: true, 
        saved: totalSaved, 
        processingTime 
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error(`Save failed: ${error.message}`);
      
      return { 
        success: false, 
        error: error.message, 
        processingTime 
      };
    }
  }

  private async processBlockTransactions(networkId: number, blockData: any) {
    // No transactions in this block
    if (!blockData.transactions || blockData.transactions.length === 0) {
      return { savedCount: 0, transactions: [] };
    }

    // Map all transactions onto entity format
    const transactions =  blockData.transactions.map((txData: any) => {
      return {
        hash: txData.hash,
        networkId: networkId,
        from: (txData.from || '').toLowerCase(),
        to: (txData.to || '').toLowerCase(),
        value: txData.value,
        blockNumber: txData.blockNumber || blockData.number,
        blockTimestamp: blockData.timestamp,
        transactionIndex: txData.transactionIndex,
        nonce: txData.nonce,
        gasPrice: txData.gasPrice || txData.maxFeePerGas,
        gasUsed: txData.gas || null,
        chainId: txData.chainId ? txData.chainId :`0x${networkId}`,
        transactionType: txData.type,
        status: null,
      }
    });
    console.log("transactions: ", transactions);

    const webhookTransactions = blockData.transactions.map((txData: any) => {
      return {
        hash: txData.hash,
        from: (txData.from || '').toLowerCase(),
        to: (txData.to || '').toLowerCase(), 
        value: txData.value,
        blockNumber: txData.blockNumber || blockData.number,
        blockTimestamp: blockData.timestamp,
        gasPrice: txData.gasPrice || txData.maxFeePerGas,
        transactionIndex: txData.transactionIndex,
        nonce: txData.nonce
      }
    });

    await Promise.all(transactions);
    this.logger.log(`Processing ${transactions.length} transactions`);
    
    try {
      await this.entityManager
        .createQueryBuilder()
        .insert()
        .into(Transaction)
        .values(transactions)
        .orIgnore() // ignore duplicates
        .execute();

      return { 
        savedCount: transactions.length, 
        transactions: webhookTransactions 
      };
    } catch (error) {
      // ignore duplicate n continue
      if (error.message.includes('duplicate key')) {
        this.logger.warn(`Skipped ${transactions.length} duplicate transactions`);
        return { savedCount: 0, transactions: webhookTransactions };
      }
      // throw if random error
      throw error;
    }
  }
}
