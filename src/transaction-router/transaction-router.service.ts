import { Injectable, Logger } from '@nestjs/common';
import { CreateTransactionRouterDto } from './dto/create-transaction-router.dto';
import { UpdateTransactionRouterDto } from './dto/update-transaction-router.dto';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { Transaction } from './entities/transactions.entity';
import { WebhookDeliveryService } from 'src/webhooks/webhook-delivery.service';
import { Network } from 'src/networks/entities/network.entity';

@Injectable()
export class TransactionRouterService {
  private readonly logger = new Logger(TransactionRouterService.name);

  constructor(
    @InjectEntityManager()
    private entityManager: EntityManager,
    private webhookDeliveryService: WebhookDeliveryService
  ) {}

  // ==================== TRANSACTION METHODS ====================
  private formatIncomingData(rawData: any): { transactions: any[], blockData?: any } {
    // Handle block-based structure (regular webhooks)
    if (rawData?.data && Array.isArray(rawData.data)) {
      const allTransactions = [];
      let latestBlockData = null;

      for (const blockData of rawData.data) {
        if (blockData?.block?.transactions) {
          const transactionsWithBlock = blockData.block.transactions.map(tx => ({
            ...tx,
            blockNumber: tx.blockNumber || blockData.block.number,
            blockTimestamp: blockData.block.timestamp
          }));
          allTransactions.push(...transactionsWithBlock);
          latestBlockData = blockData.block; // Keep latest block info
        }
      }
      
      return { transactions: allTransactions, blockData: latestBlockData };
    }

    // Handle matched transactions structure (filtered webhooks)
    if (rawData?.matchingTransactions && Array.isArray(rawData.matchingTransactions)) {
      return { transactions: rawData.matchingTransactions };
    }

    return { transactions: [] };
  }

  async saveTransactions(networkId: number, transactions: any[], blockData?: any) {
    // block data optional as matched transactions dont have block data
    if (!transactions || transactions.length === 0) {
      return { success: true, saved: 0, total: 0, transactions: [] };
    }

    // etheruem uses blocks solana doesnt use blocks  so the structure is diffrent handle later forcus on this for now 

    // Debug: Log the structure of incoming data
    this.logger.debug('Saved Booiii data structure:', JSON.stringify(transactions, null, 2));

    try {
      // Format transactions for database
      const dbTransactions = transactions.map((tx: any) => ({
        hash: tx.hash,
        networkId: networkId,
        from: tx.from ? tx.from.toLowerCase() : null,
        to: tx.to ? tx.to.toLowerCase() : null,
        value: tx.value,
        blockNumber: tx.blockNumber || blockData?.number,
        blockTimestamp: tx.blockTimestamp || blockData?.timestamp,
        transactionIndex: tx.transactionIndex,
        nonce: tx.nonce,
        gasPrice: tx.gasPrice || tx.maxFeePerGas,
        gasUsed: tx.gas || null,
        chainId: tx.chainId || `0x${networkId}`,
        transactionType: tx.type,
        status: null
      }));

      // Format transactions for webhook delivery
      const webhookTransactions = transactions.map((tx: any) => ({
        hash: tx.hash,
        from: tx.from?.toLowerCase() || null,
        to: tx.to?.toLowerCase() || null,
        value: tx.value,
        blockNumber: tx.blockNumber || blockData?.number,
        blockTimestamp: tx.blockTimestamp || blockData?.timestamp,
        gasPrice: tx.gasPrice || tx.maxFeePerGas,
        transactionIndex: tx.transactionIndex,
        nonce: tx.nonce
      }));

      // Save to database
      await this.entityManager
        .createQueryBuilder()
        .insert()
        .into(Transaction)
        .values(dbTransactions)
        .orIgnore()
        .execute();

      this.logger.log(`Processed ${transactions.length} transactions, saved ${dbTransactions.length} new ones`);

      return {
        success: true,
        saved: dbTransactions.length,
        total: transactions.length,
        transactions: webhookTransactions
      };

      } catch (error) {
        if (error.message.includes('duplicate key')) {
          this.logger.debug(`Skipped ${transactions.length} duplicate transactions`);
          
          // Still return webhook transactions even if duplicates
          const webhookTransactions = transactions.map((tx: any) => ({
            hash: tx.hash,
            from: tx.from?.toLowerCase() || null,
            to: tx.to?.toLowerCase() || null,
            value: tx.value,
            blockNumber: tx.blockNumber || blockData?.number,
            blockTimestamp: tx.blockTimestamp || blockData?.timestamp,
            gasPrice: tx.gasPrice || tx.maxFeePerGas,
            transactionIndex: tx.transactionIndex,
            nonce: tx.nonce
          }));

          return {
            success: true,
            saved: 0,
            total: transactions.length,
            transactions: webhookTransactions
          };
        }
        
        this.logger.error(`Transaction saving failed: ${error.message}`);
        return {
          success: false,
          error: error.message,
          saved: 0,
          total: transactions.length,
          transactions: []
        };
      }
    }


  async getNetwork(networkId: number): Promise<Network> {
    try {
      return await this.entityManager
        .createQueryBuilder(Network, 'network')
        .where('network.id = :networkId', { networkId })
        .getOne();
    } catch (error) {
      this.logger.error(`Failed to get network ${networkId}: ${error.message}`);
      return null;
    }
  }

  // connected to the webhookDeliveryService to deliver the webhooks 
  private async deliverWebhooks(networkId: number, transactions: any[], networkName: string) {
    try {
      this.logger.log(`WE deliverin ${transactions.length} transactions on ${networkName}`);
      
      const result = await this.webhookDeliveryService.routeTransactionsToWebhooks(
        networkId,
        transactions,
        networkName
      );

      return {
        found: result.webhooksFound,
        delivered: result.webhooksDelivered,
        failed: result.webhooksFailed,
        message: result.message
      };

    } catch (error) {
      this.logger.error(`Webhook delivery error: ${error.message}`);
      return {
        found: 0,
        delivered: 0,
        failed: 0,
        message: `Webhook delivery failed: ${error.message}`
      };
    }
  }

  // ==================== MAIN PROCESS ====================

  async processIncomingData(networkId: number, rawData: any) {
    this.logger.debug('Raw data structure:', JSON.stringify(rawData, null, 2));
    // Step 1: Validate input ( always gotta do it ) 
    const { transactions, blockData } = this.formatIncomingData(rawData);

    if (transactions.length === 0) {
      return {
        success: false,
        error: 'No transactions found in payload'
      };
    }

    // Step 2: Extract and save transactions
    const transactionResult = await this.saveTransactions(networkId, transactions, blockData);
    if(!transactionResult.success){
      return {
        success: false,
        error: `Transaction saving failed: ${transactionResult.error}`,
        transactions: { saved: 0, total: 0 }
      };
    }

    // Step 3: Get network info for webhook delievery 
    const network = await this.getNetwork(networkId);
    if (!network) {
      return {
        success: false,
        error: `Network ${networkId} not found`,
        transactions: transactionResult
      };
    }

    // Step 4: Deliver webhooks (if we have transactions)
    let webhookResult = { delivered: 0, failed: 0, found: 0, message: 'There are no ships in the sea Today' };

    if (transactionResult.transactions.length > 0) {
      webhookResult = await this.deliverWebhooks(networkId, transactionResult.transactions, network.display_name);
    }


    // Step 5: Return the pristinely formatted transactions 
    return {
      success: true,
      transactions: {
        saved: transactionResult.saved,
        total: transactionResult.total
      },
      webhooks: {
        found: webhookResult.found,
        delivered: webhookResult.delivered,
        failed: webhookResult.failed,
        message: webhookResult.message
      }
    };
  }





  
}
